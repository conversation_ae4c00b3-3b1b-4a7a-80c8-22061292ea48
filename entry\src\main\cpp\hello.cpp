#include "napi/native_api.h"
#include <multimedia/player_framework/native_avcodec_videoencoder.h>
#include <multimedia/player_framework/native_avcodec_base.h>
#include <multimedia/player_framework/native_avformat.h>
#include <multimedia/player_framework/native_avmemory.h>
#include <multimedia/player_framework/native_avcapability.h>
#include <hilog/log.h>
#include <string>
#include <memory>

#undef LOG_DOMAIN
#undef LOG_TAG
#define LOG_DOMAIN 0x3200
#define LOG_TAG "VideoEncoder"

// H.264编码器类
class H264VideoEncoder {
private:
    OH_AVCodec* encoder_;
    OH_AVFormat* format_;
    bool isConfigured_;
    bool isStarted_;
    int32_t frameCount_;

    // 编码参数
    int32_t width_;
    int32_t height_;
    int32_t bitrate_;
    double frameRate_;
    int32_t pixelFormat_;

public:
    H264VideoEncoder() : encoder_(nullptr), format_(nullptr),
                        isConfigured_(false), isStarted_(false), frameCount_(0),
                        width_(1920), height_(1080), bitrate_(2000000),
                        frameRate_(30.0), pixelFormat_(AV_PIXEL_FORMAT_YUV420P) {}

    ~H264VideoEncoder() {
        Release();
    }

    // 初始化编码器
    int32_t Initialize(int32_t width, int32_t height, int32_t bitrate, double frameRate);

    // 配置编码器
    int32_t Configure();

    // 启动编码器
    int32_t Start();

    // 编码一帧数据
    int32_t EncodeFrame(uint8_t* yuvData, size_t dataSize, uint8_t** encodedData, size_t* encodedSize);

    // 停止编码器
    int32_t Stop();

    // 释放资源
    void Release();

    // 设置编码回调
    int32_t SetCallback();
};

// 编码器回调函数
static void OnError(OH_AVCodec* codec, int32_t errorCode, void* userData) {
    OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Encoder error: %d", errorCode);
}

static void OnOutputFormatChanged(OH_AVCodec* codec, OH_AVFormat* format, void* userData) {
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_DOMAIN, LOG_TAG, "Output format changed");
}

static void OnInputBufferAvailable(OH_AVCodec* codec, uint32_t index, OH_AVMemory* data, void* userData) {
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_DOMAIN, LOG_TAG, "Input buffer available: %u", index);
}

static void OnOutputBufferAvailable(OH_AVCodec* codec, uint32_t index, OH_AVMemory* data,
                                   OH_AVCodecBufferAttr* attr, void* userData) {
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_DOMAIN, LOG_TAG, "Output buffer available: %u, size: %d",
                 index, attr->size);

    // 这里可以获取编码后的H.264数据
    uint8_t* encodedData = OH_AVMemory_GetAddr(data);
    if (encodedData && attr->size > 0) {
        // 处理编码后的数据，比如写入文件或发送到网络
        OH_LOG_Print(LOG_APP, LOG_INFO, LOG_DOMAIN, LOG_TAG, "Encoded frame size: %d", attr->size);
    }

    // 释放输出缓冲区
    OH_VideoEncoder_FreeOutputData(codec, index);
}

int32_t H264VideoEncoder::Initialize(int32_t width, int32_t height, int32_t bitrate, double frameRate) {
    width_ = width;
    height_ = height;
    bitrate_ = bitrate;
    frameRate_ = frameRate;

    // 创建H.264编码器
    encoder_ = OH_VideoEncoder_CreateByMime(OH_AVCODEC_MIMETYPE_VIDEO_AVC);
    if (!encoder_) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to create H.264 encoder");
        return AV_ERR_UNKNOWN;
    }

    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_DOMAIN, LOG_TAG, "H.264 encoder created successfully");
    return AV_ERR_OK;
}

int32_t H264VideoEncoder::Configure() {
    if (!encoder_) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Encoder not initialized");
        return AV_ERR_INVALID_VAL;
    }

    // 创建格式配置
    format_ = OH_AVFormat_Create();
    if (!format_) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to create format");
        return AV_ERR_NO_MEMORY;
    }

    // 设置编码参数
    OH_AVFormat_SetIntValue(format_, OH_MD_KEY_WIDTH, width_);
    OH_AVFormat_SetIntValue(format_, OH_MD_KEY_HEIGHT, height_);
    OH_AVFormat_SetIntValue(format_, OH_MD_KEY_PIXEL_FORMAT, pixelFormat_);
    OH_AVFormat_SetDoubleValue(format_, OH_MD_KEY_FRAME_RATE, frameRate_);
    OH_AVFormat_SetLongValue(format_, OH_MD_KEY_BITRATE, bitrate_);
    OH_AVFormat_SetIntValue(format_, OH_MD_KEY_I_FRAME_INTERVAL, 30); // I帧间隔

    // 配置编码器
    int32_t ret = OH_VideoEncoder_Configure(encoder_, format_);
    if (ret != AV_ERR_OK) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to configure encoder: %d", ret);
        return ret;
    }

    // 设置回调
    ret = SetCallback();
    if (ret != AV_ERR_OK) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to set callback: %d", ret);
        return ret;
    }

    isConfigured_ = true;
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_DOMAIN, LOG_TAG, "Encoder configured successfully");
    return AV_ERR_OK;
}

int32_t H264VideoEncoder::SetCallback() {
    OH_AVCodecAsyncCallback callback;
    callback.onError = OnError;
    callback.onStreamChanged = OnOutputFormatChanged;
    callback.onNeedInputData = OnInputBufferAvailable;
    callback.onNeedOutputData = OnOutputBufferAvailable;

    int32_t ret = OH_VideoEncoder_SetCallback(encoder_, callback, this);
    if (ret != AV_ERR_OK) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to set encoder callback: %d", ret);
        return ret;
    }

    return AV_ERR_OK;
}

int32_t H264VideoEncoder::Start() {
    if (!encoder_ || !isConfigured_) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Encoder not configured");
        return AV_ERR_INVALID_VAL;
    }

    int32_t ret = OH_VideoEncoder_Prepare(encoder_);
    if (ret != AV_ERR_OK) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to prepare encoder: %d", ret);
        return ret;
    }

    ret = OH_VideoEncoder_Start(encoder_);
    if (ret != AV_ERR_OK) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to start encoder: %d", ret);
        return ret;
    }

    isStarted_ = true;
    frameCount_ = 0;
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_DOMAIN, LOG_TAG, "Encoder started successfully");
    return AV_ERR_OK;
}

int32_t H264VideoEncoder::EncodeFrame(uint8_t* yuvData, size_t dataSize, uint8_t** encodedData, size_t* encodedSize) {
    if (!encoder_ || !isStarted_) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Encoder not started");
        return AV_ERR_INVALID_VAL;
    }

    if (!yuvData || dataSize == 0) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Invalid input data");
        return AV_ERR_INVALID_VAL;
    }

    // 获取输入缓冲区
    uint32_t index;
    int32_t ret = OH_VideoEncoder_GetInputBuffer(encoder_, &index);
    if (ret != AV_ERR_OK) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to get input buffer: %d", ret);
        return ret;
    }

    // 获取输入内存
    OH_AVMemory* inputMemory = OH_VideoEncoder_GetInputData(encoder_, index);
    if (!inputMemory) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to get input memory");
        return AV_ERR_UNKNOWN;
    }

    // 复制YUV数据到输入缓冲区
    uint8_t* inputBuffer = OH_AVMemory_GetAddr(inputMemory);
    size_t bufferSize = OH_AVMemory_GetSize(inputMemory);

    if (dataSize > bufferSize) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Input data too large: %zu > %zu", dataSize, bufferSize);
        return AV_ERR_INVALID_VAL;
    }

    memcpy(inputBuffer, yuvData, dataSize);

    // 设置缓冲区属性
    OH_AVCodecBufferAttr attr;
    attr.pts = frameCount_ * (1000000 / frameRate_); // 微秒时间戳
    attr.size = dataSize;
    attr.offset = 0;
    attr.flags = AVCODEC_BUFFER_FLAGS_NONE;

    // 推送输入数据
    ret = OH_VideoEncoder_PushInputData(encoder_, index, attr);
    if (ret != AV_ERR_OK) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to push input data: %d", ret);
        return ret;
    }

    frameCount_++;
    OH_LOG_Print(LOG_APP, LOG_DEBUG, LOG_DOMAIN, LOG_TAG, "Frame %d encoded", frameCount_);

    return AV_ERR_OK;
}

int32_t H264VideoEncoder::Stop() {
    if (!encoder_ || !isStarted_) {
        return AV_ERR_OK;
    }

    // 发送结束信号
    OH_AVCodecBufferAttr attr;
    attr.pts = 0;
    attr.size = 0;
    attr.offset = 0;
    attr.flags = AVCODEC_BUFFER_FLAGS_EOS;

    uint32_t index;
    int32_t ret = OH_VideoEncoder_GetInputBuffer(encoder_, &index);
    if (ret == AV_ERR_OK) {
        OH_VideoEncoder_PushInputData(encoder_, index, attr);
    }

    ret = OH_VideoEncoder_Stop(encoder_);
    if (ret != AV_ERR_OK) {
        OH_LOG_Print(LOG_APP, LOG_ERROR, LOG_DOMAIN, LOG_TAG, "Failed to stop encoder: %d", ret);
    }

    isStarted_ = false;
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_DOMAIN, LOG_TAG, "Encoder stopped");
    return ret;
}

void H264VideoEncoder::Release() {
    if (isStarted_) {
        Stop();
    }

    if (encoder_) {
        OH_VideoEncoder_Destroy(encoder_);
        encoder_ = nullptr;
    }

    if (format_) {
        OH_AVFormat_Destroy(format_);
        format_ = nullptr;
    }

    isConfigured_ = false;
    OH_LOG_Print(LOG_APP, LOG_INFO, LOG_DOMAIN, LOG_TAG, "Encoder released");
}

// 全局编码器实例
static std::unique_ptr<H264VideoEncoder> g_encoder = nullptr;

// NAPI函数：初始化编码器
static napi_value InitEncoder(napi_env env, napi_callback_info info) {
    size_t argc = 4;
    napi_value args[4];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 4) {
        napi_throw_error(env, nullptr, "Expected 4 arguments: width, height, bitrate, frameRate");
        return nullptr;
    }

    int32_t width, height, bitrate;
    double frameRate;

    napi_get_value_int32(env, args[0], &width);
    napi_get_value_int32(env, args[1], &height);
    napi_get_value_int32(env, args[2], &bitrate);
    napi_get_value_double(env, args[3], &frameRate);

    // 创建编码器实例
    g_encoder = std::make_unique<H264VideoEncoder>();

    int32_t ret = g_encoder->Initialize(width, height, bitrate, frameRate);
    if (ret != AV_ERR_OK) {
        g_encoder.reset();
        napi_throw_error(env, nullptr, "Failed to initialize encoder");
        return nullptr;
    }

    ret = g_encoder->Configure();
    if (ret != AV_ERR_OK) {
        g_encoder.reset();
        napi_throw_error(env, nullptr, "Failed to configure encoder");
        return nullptr;
    }

    ret = g_encoder->Start();
    if (ret != AV_ERR_OK) {
        g_encoder.reset();
        napi_throw_error(env, nullptr, "Failed to start encoder");
        return nullptr;
    }

    napi_value result;
    napi_create_int32(env, ret, &result);
    return result;
}

// NAPI函数：编码一帧数据
static napi_value EncodeFrame(napi_env env, napi_callback_info info) {
    size_t argc = 1;
    napi_value args[1];
    napi_get_cb_info(env, info, &argc, args, nullptr, nullptr);

    if (argc < 1) {
        napi_throw_error(env, nullptr, "Expected 1 argument: YUV data buffer");
        return nullptr;
    }

    if (!g_encoder) {
        napi_throw_error(env, nullptr, "Encoder not initialized");
        return nullptr;
    }

    // 获取ArrayBuffer数据
    void* data;
    size_t length;
    napi_get_arraybuffer_info(env, args[0], &data, &length);

    uint8_t* encodedData = nullptr;
    size_t encodedSize = 0;

    int32_t ret = g_encoder->EncodeFrame(static_cast<uint8_t*>(data), length, &encodedData, &encodedSize);

    napi_value result;
    napi_create_int32(env, ret, &result);
    return result;
}

// NAPI函数：停止编码器
static napi_value StopEncoder(napi_env env, napi_callback_info info) {
    if (g_encoder) {
        int32_t ret = g_encoder->Stop();
        g_encoder.reset();

        napi_value result;
        napi_create_int32(env, ret, &result);
        return result;
    }

    napi_value result;
    napi_create_int32(env, AV_ERR_OK, &result);
    return result;
}

EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports)
{
    napi_property_descriptor desc[] = {
        { "initEncoder", nullptr, InitEncoder, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "encodeFrame", nullptr, EncodeFrame, nullptr, nullptr, nullptr, napi_default, nullptr },
        { "stopEncoder", nullptr, StopEncoder, nullptr, nullptr, nullptr, napi_default, nullptr }
    };
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    return exports;
}
EXTERN_C_END

static napi_module demoModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "entry",
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

extern "C" __attribute__((constructor)) void RegisterEntryModule(void)
{
    napi_module_register(&demoModule);
}
