/**
 * H.264视频编码器TypeScript接口
 * 用于将原始YUV视频帧编码为H.264格式
 */

import libentry from 'libentry.so';

export interface EncoderConfig {
  width: number;      // 视频宽度
  height: number;     // 视频高度
  bitrate: number;    // 比特率 (bps)
  frameRate: number;  // 帧率 (fps)
}

export class H264VideoEncoder {
  private isInitialized: boolean = false;
  private config: EncoderConfig | null = null;

  /**
   * 初始化H.264编码器
   * @param config 编码器配置参数
   * @returns Promise<boolean> 初始化是否成功
   */
  async initialize(config: EncoderConfig): Promise<boolean> {
    try {
      this.config = config;
      
      // 调用native层初始化编码器
      const result = libentry.initEncoder(
        config.width,
        config.height,
        config.bitrate,
        config.frameRate
      );
      
      if (result === 0) { // AV_ERR_OK
        this.isInitialized = true;
        console.info(`H264VideoEncoder initialized: ${config.width}x${config.height}, ${config.bitrate}bps, ${config.frameRate}fps`);
        return true;
      } else {
        console.error(`Failed to initialize H264VideoEncoder, error code: ${result}`);
        return false;
      }
    } catch (error) {
      console.error('Error initializing H264VideoEncoder:', error);
      return false;
    }
  }

  /**
   * 编码一帧YUV数据
   * @param yuvData YUV420P格式的原始视频帧数据
   * @returns Promise<boolean> 编码是否成功
   */
  async encodeFrame(yuvData: ArrayBuffer): Promise<boolean> {
    if (!this.isInitialized) {
      console.error('Encoder not initialized');
      return false;
    }

    if (!yuvData || yuvData.byteLength === 0) {
      console.error('Invalid YUV data');
      return false;
    }

    try {
      // 验证数据大小是否符合YUV420P格式
      const expectedSize = this.config!.width * this.config!.height * 3 / 2;
      if (yuvData.byteLength !== expectedSize) {
        console.warn(`YUV data size mismatch. Expected: ${expectedSize}, Actual: ${yuvData.byteLength}`);
      }

      // 调用native层编码函数
      const result = libentry.encodeFrame(yuvData);
      
      if (result === 0) { // AV_ERR_OK
        return true;
      } else {
        console.error(`Failed to encode frame, error code: ${result}`);
        return false;
      }
    } catch (error) {
      console.error('Error encoding frame:', error);
      return false;
    }
  }

  /**
   * 停止编码器并释放资源
   * @returns Promise<boolean> 停止是否成功
   */
  async stop(): Promise<boolean> {
    if (!this.isInitialized) {
      return true;
    }

    try {
      const result = libentry.stopEncoder();
      this.isInitialized = false;
      this.config = null;
      
      if (result === 0) { // AV_ERR_OK
        console.info('H264VideoEncoder stopped successfully');
        return true;
      } else {
        console.error(`Failed to stop H264VideoEncoder, error code: ${result}`);
        return false;
      }
    } catch (error) {
      console.error('Error stopping H264VideoEncoder:', error);
      return false;
    }
  }

  /**
   * 获取编码器状态
   * @returns boolean 编码器是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 获取当前配置
   * @returns EncoderConfig | null 当前编码器配置
   */
  getConfig(): EncoderConfig | null {
    return this.config;
  }

  /**
   * 计算YUV420P数据大小
   * @param width 视频宽度
   * @param height 视频高度
   * @returns number YUV420P数据大小（字节）
   */
  static calculateYUV420PSize(width: number, height: number): number {
    return width * height * 3 / 2;
  }

  /**
   * 验证编码器配置参数
   * @param config 编码器配置
   * @returns boolean 配置是否有效
   */
  static validateConfig(config: EncoderConfig): boolean {
    if (!config) return false;
    
    // 检查分辨率
    if (config.width <= 0 || config.height <= 0) {
      console.error('Invalid resolution');
      return false;
    }
    
    // 检查分辨率是否为偶数（H.264要求）
    if (config.width % 2 !== 0 || config.height % 2 !== 0) {
      console.error('Width and height must be even numbers for H.264');
      return false;
    }
    
    // 检查比特率
    if (config.bitrate <= 0) {
      console.error('Invalid bitrate');
      return false;
    }
    
    // 检查帧率
    if (config.frameRate <= 0 || config.frameRate > 120) {
      console.error('Invalid frame rate');
      return false;
    }
    
    return true;
  }
}

/**
 * 创建H.264编码器实例的工厂函数
 * @param config 编码器配置
 * @returns Promise<H264VideoEncoder | null> 编码器实例或null
 */
export async function createH264Encoder(config: EncoderConfig): Promise<H264VideoEncoder | null> {
  if (!H264VideoEncoder.validateConfig(config)) {
    console.error('Invalid encoder configuration');
    return null;
  }

  const encoder = new H264VideoEncoder();
  const success = await encoder.initialize(config);
  
  if (success) {
    return encoder;
  } else {
    return null;
  }
}

/**
 * 预设的编码器配置
 */
export const EncoderPresets = {
  // 1080p高质量
  HD_1080P_HIGH: {
    width: 1920,
    height: 1080,
    bitrate: 8000000, // 8Mbps
    frameRate: 30
  } as EncoderConfig,
  
  // 1080p标准质量
  HD_1080P_STANDARD: {
    width: 1920,
    height: 1080,
    bitrate: 4000000, // 4Mbps
    frameRate: 30
  } as EncoderConfig,
  
  // 720p高质量
  HD_720P_HIGH: {
    width: 1280,
    height: 720,
    bitrate: 4000000, // 4Mbps
    frameRate: 30
  } as EncoderConfig,
  
  // 720p标准质量
  HD_720P_STANDARD: {
    width: 1280,
    height: 720,
    bitrate: 2000000, // 2Mbps
    frameRate: 30
  } as EncoderConfig,
  
  // 480p标准质量
  SD_480P: {
    width: 854,
    height: 480,
    bitrate: 1000000, // 1Mbps
    frameRate: 30
  } as EncoderConfig
};
