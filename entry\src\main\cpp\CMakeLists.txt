# the minimum version of CMake.
cmake_minimum_required(VERSION 3.4.1)
project(Camera_js)

set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

include_directories(${NATIVERENDER_ROOT_PATH}
                    ${NATIVERENDER_ROOT_PATH}/include)

add_library(entry SHARED hello.cpp)

# 链接OpenHarmony多媒体框架库
target_link_libraries(entry PUBLIC
    libace_napi.z.so
    libnative_media_codecbase.so
    libnative_media_core.so
    libnative_media_venc.so
    libhilog_ndk.z.so
)